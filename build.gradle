apply from: "config.gradle"
buildscript {
    ext.kotlin_version = '1.7.10'

    repositories {
        mavenCentral()
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "https://jitpack.io" }
        maven { url 'https://developer.huawei.com/repo/'}
        jcenter()
        google()
        maven { url "https://dl.bintray.com/teadoglibrary/MPAndroidChartFix" }

    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.tencent.bugly:tinker-support:1.2.1"
        classpath 'com.huawei.agconnect:agcp:1.4.2.300'
    }
}

allprojects {
    repositories {
        mavenCentral()
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'http://maven.int.ybm100.com/repository/maven-public/' }
        maven { url 'http://maven.int.ybm100.com/repository/maven-releases/' }
        maven { url 'http://mvn.int.ybm100.com/repository/maven-releases/' }
        maven { url 'http://mvn.int.ybm100.com/repository/maven-public/' }
        maven { url "https://jitpack.io" }
        maven { url 'https://developer.huawei.com/repo/'}
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://storage.flutter-io.cn/download.flutter.io' }


        google()
        jcenter()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

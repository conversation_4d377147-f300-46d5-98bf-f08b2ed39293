include ':app', ':common', 'xyyReport'

project(':common').projectDir = new File('librarys/common')
project(':xyyReport').projectDir = new File('librarys/xyyReport')

//include ':appupdate'
//project(':appupdate').projectDir = new File('../canary-android/appupdate')
//
//include ':apmcly'
//project(':apmcly').projectDir = new File('../apmcly/sdk')
//
//include ':platform'
//project(':platform').projectDir = new File('../XyyBeanSprouts-Platform/platform')

setBinding(new Binding([gradle: this]))
evaluate(new File(
        settingsDir.parentFile,
        'XyyBeanSproutsFlutter/.android/include_flutter.groovy'
))

include ':XyyBeanSproutsFlutter'
project(':XyyBeanSproutsFlutter').projectDir = new File('../XyyBeanSproutsFlutter')

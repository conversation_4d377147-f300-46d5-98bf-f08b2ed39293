package com.baidu.flutter_bmflocation;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.baidu.flutter_bmflocation.handlers.HandlersFactory;
import com.baidu.location.LocationClient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.flutter.embedding.engine.dart.DartExecutor;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;
import io.flutter.plugin.common.PluginRegistry;

/**
 * FlutterBmflocationPlugin
 */
public class FlutterBmflocationPlugin implements FlutterPlugin, MethodCallHandler {

  //  private static MethodChannel channel;
  private static Map<Integer, MethodChannel> channelsMap= new HashMap<>();
  //
  private static Context mContext = null;

  /* 新版接口 */
  @Override
  public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
    if (null == flutterPluginBinding) {
      return;
    }
    if (null == mContext) {
      mContext = flutterPluginBinding.getApplicationContext();
    }
    BinaryMessenger binaryMessenger;
    Log.e("guan1", "configureFlutterEngine dartExecutor:" + flutterPluginBinding.getBinaryMessenger().hashCode());
    if (flutterPluginBinding.getBinaryMessenger() instanceof DartExecutor) {
      binaryMessenger = ((DartExecutor) flutterPluginBinding.getBinaryMessenger()).getBinaryMessenger();
    } else {
      binaryMessenger = flutterPluginBinding.getBinaryMessenger();
    }
    Log.e("guan1", "configureFlutterEngine binaryMessenger:" + binaryMessenger.hashCode());

    initMethodChannel(binaryMessenger);
  }

  private void initMethodChannel(BinaryMessenger binaryMessenger) {
    if (null == binaryMessenger) {
      return;
    }

    MethodChannel channel = new MethodChannel(binaryMessenger, Constants.MethodChannelName.LOCATION_CHANNEL);
    channel.setMethodCallHandler(this);
    channelsMap.put(binaryMessenger.hashCode(), channel);
    Log.e("guan1", "configureFlutterEngine initMethodChannel binaryMessenger:" + binaryMessenger.hashCode());
    MethodChannelManager.getInstance().putLocationChannel(binaryMessenger, channel);
  }

  /* 旧版接口 */
  public static void registerWith(PluginRegistry.Registrar registrar) {
    if (null == registrar) {
      return;
    }
    if (null == mContext) {
      mContext = registrar.context();
    }
    initStaticMethodChannel(registrar.messenger());
  }

  private static void initStaticMethodChannel(BinaryMessenger binaryMessenger) {
    if (null == binaryMessenger) {
      return;
    }

    FlutterBmflocationPlugin flutterBmfPlugin = new FlutterBmflocationPlugin();
    Log.e("guan1", "configureFlutterEngine initStaticMethodChannel binaryMessenger:" + binaryMessenger.hashCode());
    MethodChannel channel = new MethodChannel(binaryMessenger, Constants.MethodChannelName.LOCATION_CHANNEL);
    channel.setMethodCallHandler(flutterBmfPlugin);
    channelsMap.put(binaryMessenger.hashCode(), channel);
    MethodChannelManager.getInstance().putLocationChannel(binaryMessenger, channel);
  }


  @Override
  public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
    if (mContext == null) {
      result.error("-1", "context is null", null);
    }

    if (call.method.equals(Constants.MethodID.LOCATION_SETAGREEPRIVACY)) {
      try {
        boolean isAgreePrivacy = (Boolean) call.arguments;
        LocationClient.setAgreePrivacy(isAgreePrivacy);
      } catch (Exception e) {
      }
    }

    HandlersFactory.getInstance(mContext).dispatchMethodHandler(mContext, call, result);
  }

  @Override
  public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
    MethodChannel methodChannel = channelsMap.get(binding.getBinaryMessenger());
    if (methodChannel != null) {
      methodChannel.setMethodCallHandler(null);
    }

  }
}

group 'com.baidu.flutter_baidu_mapapi_search'
version '1.0'

buildscript {
    repositories {
        mavenCentral()
//        maven { url 'https://maven.aliyun.com/repository/central' }
//        maven { url 'https://maven.aliyun.com/repository/jcenter' }
//        maven { url 'https://maven.aliyun.com/repository/google' }
//        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
//        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.4.1'
    }
}

rootProject.allprojects {
    repositories {
        mavenCentral()
//        maven { url 'https://maven.aliyun.com/repository/central' }
//        maven { url 'https://maven.aliyun.com/repository/jcenter' }
//        maven { url 'https://maven.aliyun.com/repository/google' }
//        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
//        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        jcenter()
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 19
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
//    implementation fileTree(includes: ['*.jar'], dir: 'libs')
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Map:7.5.0'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Search:7.5.0'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Util:7.5.0'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Location_All:9.1.8'
//    implementation rootProject.findProject(":flutter_baidu_mapapi_base")
}

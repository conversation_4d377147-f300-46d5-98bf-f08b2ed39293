package com.ybm100.app.crm.doraemon.ynb;

import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.reflect.TypeToken;
import com.google.gson.Gson;
import com.just.ynbweb.base.BaseJsHandler;
import com.ybm100.app.crm.ui.activity.pdf.PdfDisplayActivity;

import java.util.Map;

/**
 * pdf预览
 */
public class PreviewPdfHandler extends BaseJsHandler {

    public static String getClassName = PreviewPdfHandler.class.getName();

    public PreviewPdfHandler() {
    }

    @Override
    public void doExec() {
        Log.i("previewPdf", "doExec" + getArgvs() + " ");
        Map<String, String> params = new Gson().fromJson(getArgvs(), new TypeToken<Map<String, String>>(){}.getType());
        if (params == null) return;
        Intent intent = new Intent(getActivity(), PdfDisplayActivity.class);
        String url;
        String title;
        if (params.containsKey("url") && !TextUtils.isEmpty(params.get("url"))) {
            url = params.get("url");
        } else url = "";
        if (params.containsKey("title") && !TextUtils.isEmpty(params.get("title"))) {
            title = params.get("title");
        } else title = "查看文件";
        intent.putExtra("fileurl", url);
        intent.putExtra("title", title);
        getActivity().startActivity(intent);
    }
}

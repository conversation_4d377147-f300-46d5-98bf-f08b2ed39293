ext {

    apiUrl = [
            //test
            testUrl                 : '"https://crm.test.ybm100.com/app/crm/"',
            testCDN                 : '"https://upload.test.ybm100.com"',
            testStaticUrl           : '"https://t-crm-app-v4.ybm100.com/static/"',
            testH5Url               : '"https://cc.test.ybm100.com/"',
            testH5UrlDomain         : '".test.ybm100.com"',
            testMineMenuIconUrl     : '"https://crm.test.ybm100.com"',
            //开发
            devUrl                  : '"https://crm.dev.ybm100.com/app/crm/"',
            devCDN                  : '"https://upload.test.ybm100.com"',
            devStaticUrl            : '"https://t-crm-app-v4.ybm100.com/static/"',
            devH5Url                : '"https://cc.test.ybm100.com/"',
            devH5UrlDomain          : '".test.ybm100.com"',
            devMineMenuIconUrl      : '"https://crm.dev.ybm100.com"',
            //预上线
            stageUrl                : '"https://crm-new.stage.ybm100.com/app/crm/"',
            stageCDN                : '"https://upload.test.ybm100.com"',
            stageStaticUrl          : '"https://stage-app.ybm100.com/static/"',
            stageH5Url              : '"https://cc.stage.ybm100.com/"',
            stageH5UrlDomain        : '".stage.ybm100.com"',
            stageMineMenuIconUrl    : '"https://crm-new.stage.ybm100.com"',
            //mock
            mockUrl                 : '"http://rap.int.ybm100.com/mockjsdata/79/app/crm/"',
            mockCDN                 : '"https://upload.test.ybm100.com"',
            mockStaticUrl           : '"https://crm-new.stage.ybm100.com/static/"',
            //线上
            releaseUrl              : '"https://crm-app.ybm100.com/app/crm/"',
            releaseCDN              : '"https://upload.ybm100.com"',
            releaseStaticUrl        : '"https://app-v4.ybm100.com/static/"',
            releaseH5Url            : '"https://cc.ybm100.com/"',
            releaseH5UrlDomain      : '".ybm100.com"',
            releaseMineMenuIconUrl  : '"https://crm-app.ybm100.com"'

    ]
    //versionCode与versionName 需要保持一致 ，例如2.0.0 对应code200
    android = [
            applicationId    : "com.ybm100.app.crm",
            applicationTestId: "com.ybm100.app.crm.debug",
            compileSdkVersion: 33,
            minSdkVersion    : 21,
            targetSdkVersion : 33,
            versionCode      : 690,
            versionName      : "6.9.0",
            pkgName          : ''
    ]


    libsVersion = [
            supportLibraryVersion = "28.0.0",
    ]


    dependencies = [
            design         : "com.android.support:design:$rootProject.supportLibraryVersion",
            support_v7     : "com.android.support:support-v7:$rootProject.supportLibraryVersion",
            cardview_v7    : "com.android.support:cardview-v7:$rootProject.supportLibraryVersion",
            recyclerview_v7: "com.android.support:recyclerview-v7:$rootProject.supportLibraryVersion",
            appcompat_v7   : "com.android.support:appcompat-v7:$rootProject.supportLibraryVersion",
            supportV4      : "com.android.support:support-v4:$rootProject.supportLibraryVersion",
    ]

    config = [
            // 测试环境
//            UMAppKey_Test: '"5d562f844ca3573980000b82"',
            // 线上环境
//            UMAppKey     : '"5d56201e570df331c7000ba8"',

            // 新友盟账号
            // 测试环境
            UMAppKey_Test: '"5f8d2814a88dfc3eb93ad98d"',
            // 线上环境
            UMAppKey     : '"5f8d2920a88dfc3eb93adab1"',
            UMMessageSecret_Test : '"9a87218a1ad46c800885e56ddaa4358b"',
            UMMessageSecret      : '"8f07cbd8ce494a3d8175b0420793fa17"',

            //小米厂商通道
            UMXiaoMiAppID_Test  :   '"2882303761518728344"',
            UMXiaoMiAppKey_Test :   '"5911872885344"',
            UMXiaoMiAppID       :   '"2882303761518741412"',
            UMXiaoMiAppKey      :   '"5791874152412"',
            //华为厂商通道
            UMHuaWeiAppID_Test  :   103043279,
            UMHuaWeiAppID       :   103081141,


            // 百度地图
            BaiduMap_AppKey_Test:   'GU1vUjCvGRVFpTMQ00SDw6iVIGgwG19h',
            BaiduMap_AppKey     :   'DjdiByfjM8pZ2igiYketRnzFo6LVzYxl'
    ]

}

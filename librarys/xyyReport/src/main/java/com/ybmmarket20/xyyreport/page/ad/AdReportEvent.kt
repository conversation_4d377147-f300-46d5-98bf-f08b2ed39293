package com.ybmmarket20.xyyreport.page.ad

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportPageComponentExposureBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.ISpm
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

object AdReportEvent {

    //页面曝光
    @JvmStatic
    fun trackAdPv(context: Context, spm: ISpm?) {
        val pageExposureBean = ReportPageExposureBean()
        SpmLogUtil.print("广告页页面曝光")
        SpmUtil.setNewSpmE(spm)
        ReportUtil.track(context, pageExposureBean, spm, null)
    }

    //组件曝光
    @JvmStatic
    fun trackAdComponentExposure(context: Context, spmStr: String?, spmCtn: SpmBean?) {
        val componentBean = ReportPageComponentExposureBean()
        SpmLogUtil.print("广告页组件曝光")
        val spm = SpmUtil.getSpmComponentExposureFromStr(spmStr)
        spm.apply {
            spmB = spmCtn?.spmB
            spmE = spmCtn?.spmE
        }
        SpmUtil.fixSpmESession(spm)
        ReportUtil.track(context, componentBean, spm, null)
    }

    //子模块点击
    @JvmStatic
    fun trackAdSubModuleClick(context: Context, spmStr: String?, scmStr: String?, spmCtn: SpmBean?) {
        val clickBean = ReportActionSubModuleClickBean()
        SpmLogUtil.print("广告页点击")
        val spm = SpmUtil.getSpmBeanFromStr(spmStr)
        spm.apply {
            spmB = spmCtn?.spmB
            spmE = spmCtn?.spmE
        }
        SpmUtil.fixSpmESession(spm)
        val scm = SpmUtil.getScmBeanFromStr(scmStr)
        SpmUtil.setScmE(scm)
        ReportUtil.track(context, clickBean, spm, scm)
    }
}
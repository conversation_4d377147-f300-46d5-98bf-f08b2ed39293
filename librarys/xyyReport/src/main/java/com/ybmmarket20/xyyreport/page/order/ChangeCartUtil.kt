package com.ybmmarket20.xyyreport.page.order

import android.content.Context
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.ybmmarket20.xyyreport.page.cart.CartReportConstant
import com.ybmmarket20.xyyreport.page.common.addCart.AddCartPopupWindowConstant
import com.ybmmarket20.xyyreport.paramsInfo.IRowsBeanInfo
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmExtensionConstant
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.TrackData
import com.ybmmarket20.xyyreport.spm.XyyReportActivity
import org.json.JSONObject

object ChangeCartUtil {

    @JvmStatic
    fun getChangeCartParams(context: Context, rowsBeanInfo: IRowsBeanInfo?): String? {
        return try {
            if (context !is XyyReportActivity) return null
            if (rowsBeanInfo?.onOpGoods() == true && !rowsBeanInfo.onOpSingleGoods()) return null
            val spm = (context.getExtensionValue(AddCartPopupWindowConstant.EXTENSION_ADD_CART_ACTION_ITEM_SPM) as? SpmBean)?.newInstance()
            val scm = (context.getExtensionValue(AddCartPopupWindowConstant.EXTENSION_ADD_CART_ACTION_ITEM_SCM) as? ScmBean)?.newInstance()
            if (spm == null || scm == null) return null
            //spmC(组件是“0”)则不需要买点
            if (spm.spmC == "0") return null
            val qtListData = rowsBeanInfo?.getQtListData()
            val qtSkuData = rowsBeanInfo?.getSpmQtSkuData()
            val map = mutableMapOf<String, Any>(
                "spm_cnt" to spm.concat(),
                "scm_cnt" to scm.concat(),
            )
            return if(qtListData != null && qtSkuData != null &&rowsBeanInfo.isMainFrequently()) {
                map.clear()
                val obj = JSONObject()
                val listDataObj = JSONObject(qtListData)
                val skuDataObj = JSONObject(qtSkuData)
                obj.apply {
                    put("spm_cnt", spm.concat())
                    put("scm_cnt", scm.concat())
                    put("qt_list_data", listDataObj)
                    put("qt_sku_data", skuDataObj)
                }.toString()
            } else if (qtListData != null && qtSkuData != null) {
                val obj = JSONObject()
                val listDataObj = JSONObject(qtListData)
                val skuDataObj = JSONObject(qtSkuData)
                obj.apply {
                    put("spm_cnt", spm.concat())
                    put("scm_cnt", scm.concat())
                    put("qt_list_data", listDataObj)
                    put("qt_sku_data", skuDataObj)
                }.toString()
            } else {
                val obj = JSONObject()
                obj.apply {
                    put("spm_cnt", spm.concat())
                    put("scm_cnt", scm.concat())
                }.toString()
            }

        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 获取再次购买Spm
     */
    private fun getBuyAgainSpmParams(context: Context): TrackData? {
        return SpmUtil.checkAnalysisContextReturn(context) {
            val spm = it.getExtensionValue(OrderConstant.ORDER_ACTION_BUY_AGAIN_SPM_CNT)
            val scm = it.getExtensionValue(OrderConstant.ORDER_ACTION_BUY_AGAIN_SCM_CNT)
            if (spm != null && scm != null && spm is SpmBean && scm is ScmBean) {
                TrackData(spm, scm)
            } else null
        }
    }

    @JvmStatic
    fun addQtDataToParams(context: Context, p: Map<String, String>?): Map<String, String>? {
        p?: return p
        val spmP = getBuyAgainSpmParams(context)
        val map = mutableMapOf<String, Any>(
            "spm_cnt" to (spmP?.spmEntity?.concat()?: ""),
            "scm_cnt" to (spmP?.scmEntity?.concat()?: ""),
        )
        val qtData = Gson().toJson(map)
        return p.toMutableMap().apply {
            put("qtdata", qtData)
        }
    }

}
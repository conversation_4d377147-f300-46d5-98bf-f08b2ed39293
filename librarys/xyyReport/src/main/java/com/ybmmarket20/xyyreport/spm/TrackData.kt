package com.ybmmarket20.xyyreport.spm

import java.io.Serializable

data class TrackData(
    val spmEntity: SpmBean?,
    val scmEntity: ScmBean?
): Serializable {

    fun newTrackData(): TrackData {
        val spm = SpmBean(
            spmEntity?.spmA,
            spmEntity?.spmB,
            spmEntity?.spmC,
            spmEntity?.spmD,
            spmEntity?.spmE
        )
        val scm = ScmBean(
            scmEntity?.scmA,
            scmEntity?.scmB,
            scmEntity?.scmC,
            scmEntity?.scmD,
            scmEntity?.scmE
        )
        return TrackData(spm, scm)
    }
}
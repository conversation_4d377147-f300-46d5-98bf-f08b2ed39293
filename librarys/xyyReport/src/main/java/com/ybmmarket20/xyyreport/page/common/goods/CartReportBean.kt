package com.ybmmarket20.xyyreport.page.common.goods

import com.ybmmarket20.xyyreport.paramsInfo.ICartRowsBeanInfo

/**
 * 购物车商品列表埋点数据
 */
data class CartReportBean(
    var cartReportCartList: List<CartReportShop>?
) {
    constructor(): this(null)
}

/**
 * 购物车店铺埋点数据
 */
data class CartReportShop(
    var cartProdGroupList: List<CartReportProdGroup>?,
    var mShopName: String?,
    var mShopCode: String?,
    var mShopPosition: Int
): ICartRowsBeanInfo {
    override fun getShopPosition(): Int = mShopPosition

    override fun getProdGroupPosition(): Int = 0

    override fun getProdPosition(): Int = 0

    override fun getShopCode(): String? = mShopCode

    override fun getProductId(): Long = 0

    override fun getShowName(): String? = null

    override fun getShopName(): String? = mShopName
}

/**
 * 购物车商品组埋点数据
 */
data class CartReportProdGroup(
    var cartProdGroupList: List<CartReportProd>?,
    var prodGroupPosition: Int
)

/**
 * 购物车商品埋点数据
 */
data class CartReportProd(
    var mShowName: String?,
    var mShopCode: String?,
    var mShopName: String?,
    var mProductId: Long,
    var mShopPosition: Int,
    var mProdGroupPosition: Int,
    var mProdPosition: Int,
): ICartRowsBeanInfo {
    override fun getShopPosition(): Int = mShopPosition

    override fun getProdGroupPosition(): Int = mProdGroupPosition

    override fun getProdPosition(): Int = mProdPosition

    override fun getShopCode(): String? = mShopCode

    override fun getProductId(): Long = mProductId

    override fun getShowName(): String? = mShowName

    override fun getShopName(): String? = mShopName

}
package com.ybmmarket20.xyyreport.page.payment.suixinpin

import android.content.Context
import android.util.Log
import com.ybmmarket20.report.ReportPageSubModuleSearchGoodsExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.payment.IPaymentSuiXinPinGoods
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

sealed class PaymentSuiXinGoodsExposure(
    private val context: Context,
    val goodsInfo: IPaymentSuiXinPinGoods
) {
    open fun getExposureSpmC(): String? = null
    open fun getExposureSpmD(): String? = null
    private fun getExposureScmA(): String = "recommend"
    private fun getExposureScmB(): String = if (goodsInfo.getExpId().isNullOrEmpty()) "0" else goodsInfo.getExpId()!!
    private fun getExposureScmC(): String = "all_0"
    private fun getExposureScmD(): String = "prod-${goodsInfo.getProductSkuSkuId()}"
    private fun getExposureScmE(): String? = goodsInfo.getSuiXinPinScmId()
    open fun logMessage(): String = ""

    fun track() {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtnNewInstance()?.apply {
                spmC = getExposureSpmC()
                spmD = getExposureSpmD()
            }
            val scm = ScmBean().apply {
                scmA = getExposureScmA()
                scmB = getExposureScmB()
                scmC = getExposureScmC()
                scmD = getExposureScmD()
                scmE = getExposureScmE()
            }
            //提单页-商品曝光
            val event = ReportPageSubModuleSearchGoodsExposureBean().apply {
                productId = goodsInfo.getLongSkuId()
                productName = goodsInfo.getSkuName()
                qtListData = goodsInfo.getSuiXinPinQtListData()
                qtSkuData = goodsInfo.getQtSkuData()
            }
            ReportUtil.track(context, event, spm, scm)

            SpmLogUtil.print(logMessage())
        }
    }
}

/**
 * 随心拼View
 */
class SuiXinGoodsViewExposure(context: Context, goodsInfo: IPaymentSuiXinPinGoods) :
    PaymentSuiXinGoodsExposure(context, goodsInfo) {
    override fun getExposureSpmC(): String = "arbitraryList@5"
    override fun getExposureSpmD(): String = "prod@${goodsInfo.getRank()}"
    override fun logMessage(): String = "提单页-随心拼-商品曝光"
}

/**
 * 顺手买View
 */
class RecommendPayGoodsViewExposure(context: Context, goodsInfo: IPaymentSuiXinPinGoods) :
    PaymentSuiXinGoodsExposure(context, goodsInfo) {
    override fun getExposureSpmC(): String = "convenientList@5"
    override fun getExposureSpmD(): String = "prod@${goodsInfo.getRank()}"
    override fun logMessage(): String = "提单页-顺手买-商品曝光"
}

/**
 * 随心拼弹窗
 */
class RecommendPayGoodsViewPopWindowExposure(context: Context, goodsInfo: IPaymentSuiXinPinGoods) :
    PaymentSuiXinGoodsExposure(context, goodsInfo) {
    override fun getExposureSpmC(): String = "convenientList@5"
    override fun getExposureSpmD(): String {
        Log.i("scmRank", goodsInfo.getRank()+"")
        return "ftFloatBuyMore@Z_prod@${goodsInfo.getRank()}"
    }
    override fun logMessage(): String = "提单页-随心拼弹窗-商品曝光"
}

/**
 * 顺手买弹窗
 */
class SuiXinGoodsViewPopWindowExposure(context: Context, goodsInfo: IPaymentSuiXinPinGoods) :
    PaymentSuiXinGoodsExposure(context, goodsInfo) {
    override fun getExposureSpmC(): String = "arbitraryList@5"
    override fun getExposureSpmD(): String = "ftFloatBuyMore@Z_prod@${goodsInfo.getRank()}"
    override fun logMessage(): String = "提单页-顺手买弹窗-商品曝光"
}
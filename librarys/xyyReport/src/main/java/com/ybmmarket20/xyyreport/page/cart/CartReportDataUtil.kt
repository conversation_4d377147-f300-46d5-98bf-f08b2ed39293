package com.ybmmarket20.xyyreport.page.cart

import android.content.Context
import com.google.gson.Gson
import com.ybmmarket20.xyyreport.page.common.goods.CartReportBean
import com.ybmmarket20.xyyreport.page.common.goods.CartReportProd
import com.ybmmarket20.xyyreport.page.common.goods.CartReportProdGroup
import com.ybmmarket20.xyyreport.page.common.goods.CartReportShop
import com.ybmmarket20.xyyreport.paramsInfo.ICartRowsBeanInfo
import com.ybmmarket20.xyyreport.spm.ISpm
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.TrackData
import com.ybmmarket20.xyyreport.spm.XyyReportActivity

object CartReportDataUtil {

    var mCartData = CartReportBean()
    var mCartReportShopList: List<CartReportShop>? = null
    var mCartReportProdGroupList: List<CartReportProdGroup>? = null
    var mCartReportProdMap: MutableMap<Long, CartReportProd>? = null
    var mScmId: String? = null

    /**
     * 获取商品信息
     */
    @JvmStatic
    fun getProdInfo(prodId: Long): ICartRowsBeanInfo? {
        return mCartReportProdMap?.get(prodId)
    }

    /**
     * 获取店铺信息
     */
    @JvmStatic
    fun getShopInfo(shopCode: String?): ICartRowsBeanInfo? {
        return mCartReportShopList?.find { it.getShopCode() == shopCode }
    }

    /**
     * 获取scmId
     */
    @JvmStatic
    fun getScmId(): String? {
        return mScmId
    }

    /**
     * 清除购物车spm参数
     */
    @JvmStatic
    fun clearAddCartSpmParams(reportAct: XyyReportActivity) {
        reportAct.putExtension(CartReportConstant.CART_REPORT_ADD_CART_SPM_CNT, null)
        reportAct.putExtension(CartReportConstant.CART_REPORT_ADD_CART_SCM_CNT, null)
    }

    /**
     * 获取购物车加购Spm
     */
    private fun getAddCartSpmParams(context: Context): TrackData? {
        return SpmUtil.checkAnalysisContextReturn(context) {
            val spm = it.getExtensionValue(CartReportConstant.CART_REPORT_ADD_CART_SPM_CNT)
            val scm = it.getExtensionValue(CartReportConstant.CART_REPORT_ADD_CART_SCM_CNT)
            if (spm != null && scm != null && spm is SpmBean && scm is ScmBean) {
                TrackData(spm, scm)
            } else null
        }
    }

    @JvmStatic
    fun addQtDataToParams(context: Context, p: Map<String, String>?): Map<String, String>? {
        p?: return p
        val spmP = getAddCartSpmParams(context)
        val map = mutableMapOf<String, Any>(
            "spm_cnt" to (spmP?.spmEntity?.concat()?: ""),
            "scm_cnt" to (spmP?.scmEntity?.concat()?: ""),
        )
        val qtData = Gson().toJson(map)
        return p.toMutableMap().apply {
            put("qtdata", qtData)
        }
    }

}
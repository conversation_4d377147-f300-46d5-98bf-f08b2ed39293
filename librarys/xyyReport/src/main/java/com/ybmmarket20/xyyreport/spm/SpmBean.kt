package com.ybmmarket20.xyyreport.spm

import java.io.Serializable

data class SpmBean(
    var spmA: String? = SpmConstant.SPM_A,
    var spmB: String?,
    var spmC: String?,
    var spmD: String?,
    var spmE: String?,
): ISpm, Serializable {
    constructor(): this(SpmConstant.SPM_A, null, null, null, null)

    override fun concat(): String {
        return "$spmA.$spmB.${spmC?: SpmConstant.SPM_DEFAULT}.${spmD?: SpmConstant.SPM_DEFAULT}.$spmE"
    }

    fun newInstance() = SpmBean(spmA, spmB, spmC, spmD, spmE)
}

data class ScmBean(
    var scmA: String?,
    var scmB: String?,
    var scmC: String?,
    var scmD: String?,
    var scmE: String?,
): ISpm, Serializable {

    constructor(): this(null, null, null, null, null)

    override fun concat(): String {
        return "$scmA.$scmB.${scmC?: SpmConstant.SPM_DEFAULT}.${scmD?: SpmConstant.SPM_DEFAULT}.$scmE"
    }

    fun newInstance() = ScmBean(scmA, scmB, scmC, scmD, scmE)

}

interface ISpm {
    fun concat(): String
}
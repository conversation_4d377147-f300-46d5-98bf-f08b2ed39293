package com.ybmmarket20.xyyreport.page.searchOrder

import android.content.Context
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

object SearchOrderStartPageReport {

    @JvmStatic
    fun pvTrack(context: Context): SpmBean {
        SpmLogUtil.print("订单搜索中间页-PV")
        val spm = SpmUtil.getSpmPv("orderSearchMiddle_0-0_0")
        val pe = ReportPageExposureBean()
        ReportUtil.track(context, pe, spm, null)
        return spm
    }
}
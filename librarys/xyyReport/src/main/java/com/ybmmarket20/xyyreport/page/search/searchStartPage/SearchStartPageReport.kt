package com.ybmmarket20.xyyreport.page.search.searchStartPage

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportActionSubModuleClickWithUrlBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.TrackData

/**
 * 搜索启动页
 */
object SearchStartPageReport {

    fun searchStartPagePv(context: Context, pageCode: String?): SpmBean {
        SpmLogUtil.print("搜索-中间页-PV")
        val spm = SpmUtil.getSpmPv(pageCode)
        val pe = ReportPageExposureBean()
        ReportUtil.track(context, pe, spm, null)
        return spm
    }

    fun searchStartPageSugClick(context: Context, trackData: TrackData?) {
        val click = ReportActionSubModuleClickBean()
        ReportUtil.track(context, click, trackData?.spmEntity, trackData?.scmEntity)
    }

    fun searchStartPageSugClickWithUrl(context: Context, trackData: TrackData?, url: String?) {
        val click = ReportActionSubModuleClickWithUrlBean().apply {
            this.url = url
        }
        ReportUtil.track(context, click, trackData?.spmEntity, trackData?.scmEntity)
    }
}
package com.ybmmarket20.xyyreport.page.productDetail

import android.content.Context

/**
 * 商详
 */
class ProductDetailReportEvent {

    companion object {

        /**
         * 商详pv
         */
        @JvmStatic
        fun pv(context: Context, productId: String) {
//            val iReport = ReportPageExposureBean()
//            ReportUtil.pvTrack(context, iReport, "PD_$productId-0_0")
        }

        /**
         * 底部组件曝光
         */
        @JvmStatic
        fun bottomComponentExposure(context: Context) {
//            val iReport = ReportPageComponentExposureBean()
//            ReportUtil.componentExposureTrack(context, iReport, "ftProdDet@Z")
        }

        /**
         * 商品按钮点击
         */
        @JvmStatic
        fun bottomComponentBtnClick(context: Context, btnPosition: Int, btnContent: String) {
//            val iReport = ReportActionButtonClickBean()
//            ReportUtil.clickTrack(context, iReport, "ftProdDet@Z", "btn@$btnPosition", "T-$btnContent")
        }


    }
}
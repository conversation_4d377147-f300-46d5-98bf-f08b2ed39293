package com.ybmmarket20.xyyreport.page.orderList

object OrderListReportUtil {

    //tab信息status为key
    private val mOrderStatusMap = mutableMapOf<Int, OrderStatusInfo>()
    //tab信息埋点信息为key
    private val mOrderReportMap = mutableMapOf<String, OrderStatusInfo>()

    @JvmStatic
    fun initOrderListReportInfo(statusType: Int, statusText: String?) {
        val orderReportTag = when(statusType) {
            //全部
            0 -> "QB"
            //待支付
            10 -> "DZF"
            //待配送
            1 -> "DPS"
            //配送中
            2 -> "PSZ"
            //完成
            3 -> "WC"
            //待评价
            101 -> "DPJ"
            //退款/售后
            90 -> ""
            else -> ""
        }
        val orderStatusInfo = OrderStatusInfo(statusType, statusText, orderReportTag)
        mOrderStatusMap[statusType] = orderStatusInfo
        mOrderReportMap[orderReportTag] = orderStatusInfo
    }

    @JvmStatic
    fun getOrderStatusText(statusType: Int?): String {
        return when(statusType) {
            1 -> "审核中"
            7 -> "出库中"
            2 -> "配送中"
            3 -> "已完成"
            4 -> "已取消"
            6 -> "已拆单"
            10 -> "待支付"
            21 -> "已拒签"
            20 -> "已送达"
            90 -> "退款审核中"
            91 -> "已退款"
            else -> ""
        }
    }


    @JvmStatic
    fun getOrderStatusInfoByStatus(statusType: Int): OrderStatusInfo? {
        return mOrderStatusMap[statusType]
    }

    @JvmStatic
    fun getOrderStatusInfoByReport(orderReportTag: String?): OrderStatusInfo? {
        return if (orderReportTag == null) null else mOrderReportMap[orderReportTag]
    }

}
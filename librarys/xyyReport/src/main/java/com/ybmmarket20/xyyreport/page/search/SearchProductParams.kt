package com.ybmmarket20.xyyreport.page.search

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ybmmarket20.xyyreport.page.ReportListCreateBean
import java.net.URLDecoder

/**
 * 解析搜索筛选参数
 */
class SearchProductParams(
    val requestParams: Map<String, String?>,
    private val mDynamicLabelSelectedMap: Map<String, String>?,
    private val selectedShopNames: String?
) {

    private val mListCreateBean = ReportListCreateBean()

    /**
     * 解析筛选参数
     */
    fun parseFilterParams(): ReportListCreateBean {
        //排序
        parseSort()
        //是否过滤
        parseIsFilter()
        //厂家
        parseManufacturers()
        //商家
        parseDistributors()
        //规格
        parseSpecs()
        //类型
        parseDrugClassifications()
        //有效期
        parseNearEffect()
        //分类
        parseCategories()
        //最低价格
        parseMinPrice()
        //最高价格
        parseMaxPrice()
        //动态标签
        parseDynamicLabel()
        return mListCreateBean
    }

    /**
     * 排序
     */
    private fun parseSort() {
        if (requestParams.containsKey("sortStrategy") && !requestParams["sortStrategy"].isNullOrEmpty()) {
            val sort = when (requestParams["sortStrategy"]) {
                "1" -> "默认"
                "2" -> "销量从高到低"
                "3" -> "价格从低到高"
                else -> ""
            }
            mListCreateBean.sort = sort
        }
    }

    /**
     * 是否有过滤
     */
    private fun parseIsFilter() {
        if (requestParams.containsKey("isFilter") && !requestParams["isFilter"].isNullOrEmpty()) {
            mListCreateBean.isFilter = requestParams["isFilter"]!!.toInt()
        }
    }

    /**
     * 厂家
     */
    private fun parseManufacturers() {
        try {
            if (requestParams.containsKey("manufacturers") && !requestParams["manufacturers"].isNullOrEmpty()) {
                val manufacturers = Gson().fromJson<Array<String>>(
                    urlDecode(requestParams["manufacturers"]),
                    object : TypeToken<Array<String>>() {}.type
                )
                if (manufacturers.isNotEmpty()) {
                    mListCreateBean.manufacturers = manufacturers
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 商家
     */
    private fun parseDistributors() {
        if (selectedShopNames.isNullOrEmpty()) return
        val shopArray = selectedShopNames.split(",")
        if (shopArray.isEmpty()) return
        mListCreateBean.distributors = shopArray.toTypedArray()
    }

    /**
     * 规格
     */
    private fun parseSpecs() {
        try {
            if (requestParams.containsKey("specs") && !requestParams["specs"].isNullOrEmpty()) {
                val specArray = Gson().fromJson<Array<String>>(
                    urlDecode(requestParams["specs"]),
                    object : TypeToken<Array<String>>() {}.type
                )
                if (specArray.isNotEmpty()) {
                    mListCreateBean.specs = specArray
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 类型
     */
    private fun parseDrugClassifications() {
        if (requestParams.containsKey("drugClassificationsStr") && !requestParams["drugClassificationsStr"].isNullOrEmpty()) {
            val drugClassificationsStr = urlDecode(requestParams["drugClassificationsStr"])
            val specArray = drugClassificationsStr?.split(",")
            if (!specArray.isNullOrEmpty()) {
                mListCreateBean.drugClassifications = specArray.map {
                    when (it) {
                        "1" -> "甲类OTC"
                        "2" -> "乙类OTC"
                        "3" -> "处方药RX"
                        else -> "其他"
                    }
                }.toTypedArray()
            }
        }
    }

    /**
     * 有效期
     */
    private fun parseNearEffect() {
        if (requestParams.containsKey("nearEffect") && !requestParams["nearEffect"].isNullOrEmpty()) {
            mListCreateBean.periodValidity = when (requestParams["nearEffect"]) {
                "1" -> "6个月以下"
                "2" -> "6-12个月"
                "3" -> "12个月以上"
                else -> "15个月以上"
            }
        }
    }

    /**
     * 类型
     */
    private fun parseCategories() {
        if (requestParams.containsKey("categoryIdsStr") && !requestParams["categoryIdsStr"].isNullOrEmpty()) {
            val categoryIdsStr = urlDecode(requestParams["categoryIdsStr"])
            mListCreateBean.categorys = categoryIdsStr?.split(",")?.toTypedArray()
        }
    }

    /**
     * 最低价格
     */
    private fun parseMinPrice() {
        if (requestParams.containsKey("minPrice") && !requestParams["minPrice"].isNullOrEmpty()) {
            mListCreateBean.minPrice = requestParams["minPrice"]!!.toDouble()
        }
    }

    /**
     * 最高价格
     */
    private fun parseMaxPrice() {
        if (requestParams.containsKey("maxPrice") && !requestParams["maxPrice"].isNullOrEmpty()) {
            mListCreateBean.maxPrice = requestParams["maxPrice"]!!.toDouble()
        }
    }

    /**
     * 动态标签
     */
    private fun parseDynamicLabel() {
        if (!mDynamicLabelSelectedMap.isNullOrEmpty()) {
            val dynamicNames = mutableListOf<String>()
            val dynamicTypes = mutableListOf<String>()
            mDynamicLabelSelectedMap.forEach {
                dynamicTypes.add(it.key)
                dynamicNames.add(it.value)
            }
            mListCreateBean.dynamicFilterNames = dynamicNames.toTypedArray()
            mListCreateBean.dynamicFilterTypes = dynamicTypes.toTypedArray()
        }
    }

    private fun urlDecode(str: String?): String? = URLDecoder.decode(str, "UTF-8")
}
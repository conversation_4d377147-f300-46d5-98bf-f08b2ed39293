package com.ybmmarket20.xyyreport.page.home

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportActionSubModuleGoodsClickBean
import com.ybmmarket20.report.ReportPageComponentExposureBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.report.ReportPageSubModuleExposureBean
import com.ybmmarket20.report.ReportPageSubModuleGoodsExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.ISpm
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmConstant
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.TrackData

object HomeReportEvent {

    //页面曝光
    @JvmStatic
    fun trackHomePagePv(context: Context, trackData: TrackData?) {
        val pageExposureBean = ReportPageExposureBean()
        ReportUtil.pvTrack(context, pageExposureBean, trackData?.spmEntity)
    }

    //组件曝光
    @JvmStatic
    fun trackHomeComponentExposure(context: Context, spm: ISpm?) {
        val componentBean = ReportPageComponentExposureBean()
        ReportUtil.componentExposureTrack(context, componentBean, spm)
    }

    //子模块点击
    @JvmStatic
    fun trackHomeSubComponentClick(context: Context, spm: ISpm?, scm: ISpm?) {
        val clickBean = ReportActionSubModuleClickBean()
        ReportUtil.clickTrack(context, clickBean, spm, scm)
    }

    //子模块曝光
    @JvmStatic
    fun trackHomeSubComponentExposure(context: Context, spm: ISpm?, scm: ISpm?) {
        val exposureBean = ReportPageSubModuleExposureBean()
        SpmUtil.setSpmE(context, spm)
        SpmUtil.setScmE(scm)
        ReportUtil.track(context, exposureBean, spm, scm)
    }

    /**
     * 子模块商品点击 - 移除极光埋点 action_list_product_click (保留搜索页面)
     */
    @JvmStatic
    fun trackHomeSubComponentGoodsClick(context: Context, spm: ISpm?, scm: ISpm?, productId: Long?, productName: String?) {
    //子模块商品点击 - 移除极光埋点 action_list_product_click (保留搜索页面)
        val clickBean = ReportActionSubModuleGoodsClickBean()
        clickBean.productId = productId
        clickBean.productName = productName
        ReportUtil.track(context, clickBean, spm, scm)
    }

    /**
     * 子模块商品曝光
     */
    @JvmStatic
    fun trackHomeSubComponentGoodsExposure(context: Context, spm: ISpm?, scm: ISpm?, productId: Long?, productName: String?) {
        //子模块商品曝光
        val exposureBean = ReportPageSubModuleGoodsExposureBean()
        exposureBean.productId = productId
        exposureBean.productName = productName
        ReportUtil.goodsExposureTrack(context, exposureBean, spm, scm)
    }


    private var mBigWheelSpm: SpmBean? = null

    /**
     * 大转盘点击
     */
    fun onAlertClick(context: Context, spm: ISpm?, scm: ISpm?) {
        SpmLogUtil.print("首页大转盘点击")
        SpmUtil.setSpmE(context, spm)
        if (spm is SpmBean) {
            spm.spmE = mBigWheelSpm?.spmE
        }
        SpmUtil.fixSpmESession(spm)
        SpmUtil.setScmE(scm)
        ReportUtil.track(context, ReportActionSubModuleClickBean(), spm, scm)
    }

    /**
     * 大转盘曝光PV
     */
    fun onHomeBigWheelAlertPV(context: Context, spm: ISpm?) {
        SpmLogUtil.print("首页大转盘PV")
        val newSpm = if (spm is SpmBean) {
            SpmBean(
                spm.spmA,
                spm.spmB,
                SpmConstant.SPM_DEFAULT,
                SpmConstant.SPM_DEFAULT,
                null
            ).also(SpmUtil::setNewSpmE)
        } else null
        mBigWheelSpm = newSpm
        ReportUtil.track(context, ReportPageExposureBean(), newSpm, null)
    }

    /**
     * 首页弹窗
     */
    fun onDialogCustomExposure(context: Context, spm: ISpm?) {
        SpmLogUtil.print("首页弹窗-曝光")
        SpmUtil.setNewSpmE(spm)
        ReportUtil.track(context, ReportPageExposureBean(), spm, null)
    }

    fun onDialogImageExposure(context: Context, trackData: TrackData?) {
        SpmLogUtil.print("首页弹窗-组件-图片曝光")
        ReportUtil.track(context, ReportPageComponentExposureBean(), trackData?.spmEntity, trackData?.scmEntity)
    }

    fun onDialogImageClick(context: Context, trackData: TrackData?) {
        SpmLogUtil.print("首页弹窗-组件-图片点击点击")
        SpmUtil.setScmE(trackData?.scmEntity)
        SpmUtil.fixSpmESession(trackData?.spmEntity)
        ReportUtil.track(context, ReportActionSubModuleClickBean(), trackData?.spmEntity, trackData?.scmEntity)
    }

    fun onDialogCouponExposure(context: Context, trackData: TrackData?) {
        SpmLogUtil.print("首页弹窗-组件-优惠券曝光")
        ReportUtil.track(context, ReportPageComponentExposureBean(), trackData?.spmEntity, trackData?.scmEntity)
    }

    fun onDialogCouponClick(context: Context, trackData: TrackData?) {
        SpmLogUtil.print("首页弹窗-组件-优惠券点击")
        SpmUtil.setScmE(trackData?.scmEntity)
        SpmUtil.fixSpmESession(trackData?.spmEntity)
        ReportUtil.track(context, ReportActionSubModuleClickBean(), trackData?.spmEntity, trackData?.scmEntity)
    }
}
package com.ybmmarket20.xyyreport.page.home

class HomeSubModuleRecordUtil {

    /**
     * 首页快捷入口曝光缓存
     */
    private val mFastEntryExposureRecord = mutableMapOf<String?, MutableSet<String?>?>()

    companion object {
        private var instance: HomeSubModuleRecordUtil? = null
            get() {
                if (field == null) {
                    field = HomeSubModuleRecordUtil()
                }
                return field
            }
        fun get(): HomeSubModuleRecordUtil{
            return instance!!
        }
    }

    fun isContainsFastEntryRecord(page: String?, itemId: String?): Boolean {
        if (page == null || itemId == null) return false
        if (mFastEntryExposureRecord.containsKey(page)) {
            return mFastEntryExposureRecord[page]?.contains(itemId)?: false
        }
        return false
    }

    fun addFastEntryRecord(page: String?, itemId: String?) {
        if (page == null || itemId == null) return
        if (!mFastEntryExposureRecord.containsKey(page)) {
            mFastEntryExposureRecord[page] = mutableSetOf(itemId)
        }
        mFastEntryExposureRecord[page]!!.add(itemId)
    }

    fun onClear() {
        mFastEntryExposureRecord.clear()
    }

}
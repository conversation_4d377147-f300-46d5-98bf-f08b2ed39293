package com.ybmmarket20.xyyreport.page.test

import com.xyy.xyyreport.ReportName
import com.xyy.xyyreport.ReportParam

@ReportName("ReportBean")
class TestEventBean {
    @ReportParam("StringParam")
    var test1: String? = null
    @ReportParam("DoubleParam")
    var testDouble: Double = 0.0
    @ReportParam("ShortParam")
    var testShort: Short = 1
    @ReportParam("LongParam")
    var testLong: Long = 0
    //    @EventParam("Float")
//    val testFloat: Float = 2.0f
    @ReportParam("IntParam")
    var testInt: Int = 3
    @ReportParam("BooleanParam")
    var testBoolean: Boolean = false
}

@ReportName("TestEventBean1")
class TestEventBean1 {
    @ReportParam("StringParam")
    val test1: String? = null
    @ReportParam("DoubleParam")
    val testDouble: Double = 0.0
    @ReportParam("ShortParam")
    val testShort: Short = 1
    @ReportParam("LongParam")
    val testLong: Long = 0
    //    @EventParam("Float")
//    val testFloat: Float = 2.0f
    @ReportParam("IntParam")
    val testInt: Int = 3
    @ReportParam("BooleanParam")
    val testBoolean: Boolean = false
}
package com.ybmmarket20.xyyreport.page.payment

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

object PaymentReport {

    fun pvTrack(context: Context) {
        val spm = SpmUtil.getSpmPv("orderPendingConfirmation_0-0_0")
        SpmLogUtil.print("提单页-PV")
        ReportUtil.pvTrack(context, ReportPageExposureBean(), spm)
    }

    fun trackSubmitOrderClick(context: Context, text: String?) {
        SpmLogUtil.print("提单页-提单按钮点击")
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "ftSubmitOrder@Z"
                spmD = "btn@1"
            }
            val scm = ScmBean(
                "appFE",
                "0",
                "all_0",
                "text-$text",
                ""
            )
            SpmUtil.setSpmE(context, spm)
            SpmUtil.setScmE(scm)
            it.putExtension(PaymentReportConstant.PAYMENT_REPORT_ORDER_CONFIRM_CLICK_SPM, spm)
            it.putExtension(PaymentReportConstant.PAYMENT_REPORT_ORDER_CONFIRM_CLICK_SCM, scm)
            ReportUtil.track(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

}
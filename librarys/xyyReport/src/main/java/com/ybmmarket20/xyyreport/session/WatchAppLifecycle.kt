package com.ybmmarket20.xyyreport.session

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ProcessLifecycleOwner
import com.ybmmarket20.xyyreport.SpmLogUtil

/**
 * 观察应用进入后台和回到前台
 */
class WatchAppLifecycle {

    companion object {

        /**
         * 是否已经注册过
         */
        private var isRegister = false

        private var instance: WatchAppLifecycle? = null
            get() {
                if (field == null) {
                    field = WatchAppLifecycle()
                }
                return field
            }

        @Synchronized
        fun get(): WatchAppLifecycle {
            return instance!!
        }

        //超时阈值
        const val TIME_OUT_THRESHOLD = 30 * 60 * 1000L
//        const val TIME_OUT_THRESHOLD = 3 * 1000L
    }

    var preTime: Long = 0L

    fun register() {
        if (isRegister) return
        isRegister = true
        ProcessLifecycleOwner.get().lifecycle.addObserver(WatchAppLifecycleObserver())
    }

    inner class WatchAppLifecycleObserver: LifecycleObserver {
        /**
         * App创建时执行一次
         */
        @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
        fun onCreate(){
            SessionManager.get().newSession()
        }

        /**
         * App回到前台时被调用
         */
        @OnLifecycleEvent(Lifecycle.Event.ON_START)
        fun onStart(){
            SpmLogUtil.print("检查session,应用进入前台")
            val curTime = System.currentTimeMillis()
            val diff = curTime - preTime
            if (diff != curTime) {
                SpmLogUtil.print("检查session,后台保持时长：${diff}ms")
            }
            if (diff > TIME_OUT_THRESHOLD) {
                SessionManager.get().newSession()
            }
        }

        /**
         * App回到前台时被调用
         */
        @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
        fun onResume(){

        }

        /**
         * App退到后台时调用
         */
        @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
        fun onPause(){

        }

        /**
         *  App退到后台时调用
         */
        @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
        fun onStop(){
            preTime = System.currentTimeMillis()
            SpmLogUtil.print("检查session,应用进入后台")
        }

        /**
         * 系统不会分发ON_DESTROY事件，永远不会被调用
         */
        @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
        fun onDestroy(){

        }
    }
}
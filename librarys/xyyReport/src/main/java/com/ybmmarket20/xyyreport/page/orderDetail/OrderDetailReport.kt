package com.ybmmarket20.xyyreport.page.orderDetail

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.order.OrderConstant
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

object OrderDetailReport {

    @JvmStatic
    fun pvTrack(context: Context, orderNo: String?) {
        val spm = SpmUtil.getSpmPv("orderDetail_${orderNo}-0_0")
        SpmLogUtil.print("订单详情-PV")
        ReportUtil.pvTrack(context, ReportPageExposureBean(), spm)
        SpmUtil.checkAnalysisContext(context) {
            it.putExtension(OrderConstant.ORDER_ACTION_PAGE_IS_LIST, false)
        }
    }

    @JvmStatic
    fun trackBuyAgainClick(context: Context) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "ftorderDetail@Z"
                spmD = "btn@2"
            }
            val scm = ScmBean(
                "order",
                "0",
                "all_0",
                "text-再次购买",
                null
            )
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

    @JvmStatic
    fun clickGoldCollectCertificate(context: Context, fileType: String?, fileId: String?) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "orderService@4"
                spmD = "btn@6"
            }
            val scm = ScmBean().apply {
                scmA = "order"
                scmB = "0"
                scmC = "all_0"
                scmD = "text-购物金平台代收款说明查看_fileType-${fileType}_fileId-${fileId}"
            }
            SpmLogUtil.print("订单详情-购物金平台代收款说明查看-点击")
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }
}
package com.ybmmarket20.xyyreport.page.search

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.xyy.xyyreport.IReport
import com.ybmmarket20.report.ReportActionSearchProductButtonClickBean
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportActionSubModuleGroupCombinationClickBean
import com.ybmmarket20.report.ReportActionSubModuleSearchGoodsClickBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.report.ReportPageSearchExposureBean
import com.ybmmarket20.report.ReportPageSubModuleSearchGoodsExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.common.addCart.AddCartPopupWindowConstant
import com.ybmmarket20.xyyreport.page.common.addCart.AddCartPopupWindowReport
import com.ybmmarket20.xyyreport.paramsInfo.IRowsBeanInfo
import com.ybmmarket20.xyyreport.session.SessionManager
import com.ybmmarket20.xyyreport.spm.ISpm
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmExtensionConstant
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.TrackData
import com.ybmmarket20.xyyreport.spm.XyyReportActivity

/**
 * 大搜
 */
object SearchProductReport {

    //商品Item按钮点击-立即参团
    const val SEARCH_ITEM_BTN_CLICK_SPELL_GROUP = "1"
    //商品Item按钮点击-批购包邮
    const val SEARCH_ITEM_BTN_CLICK_WHOLE_SALE = "1"
    //商品Item按钮点击-加购
    const val SEARCH_ITEM_BTN_CLICK_ADD_CART = "1"
    //商品Item按钮点击-减购
    const val SEARCH_ITEM_BTN_CLICK_SUBTRACT = "1"
    //商品Item按钮点击-加购数量
    const val SEARCH_ITEM_BTN_CLICK_ADD_COUNT = "2"
    //商品Item按钮点击-加号
    const val SEARCH_ITEM_BTN_CLICK_ADD = "3"
    //商品Item按钮点击-更多优惠
    private const val SEARCH_ITEM_BTN_CLICK_MORE = "4"
    //商品Item按钮点击-进店
    private const val SEARCH_ITEM_BTN_CLICK_SHOP_ENTRY = "5"
    //商品Item按钮点击-店铺同款
    private const val SEARCH_ITEM_BTN_CLICK_SHOP_SAME = "6"

    fun searchStartPagePv(context: Context, pageCode: String?, keyWord: String?) {
        SpmLogUtil.print("搜索-结果页-PV")
        val spm = SpmUtil.getSpmPv(pageCode)
        val pe = ReportPageSearchExposureBean().apply {
            this.keyWord = keyWord
        }
        SpmUtil.checkAnalysisContextAndEnable(context) {
            ReportUtil.pvTrack(context, pe, spm)
        }
    }

    fun searchStartPageSugClick(context: Context, trackData: TrackData?) {
        SpmLogUtil.print("搜索-结果页-sug点击")
        val click = ReportActionSubModuleClickBean()
        SpmUtil.checkAnalysisContextAndEnable(context) {
            ReportUtil.clickTrack(context, click, trackData?.spmEntity, trackData?.scmEntity)
        }
    }

    fun searchProductListCreate(context: Context, trackData: TrackData?, params: Map<String, String?>?
                                , dynamicLabelSelectedMap: Map<String, String>?, selectedShopNames: String?, qtListData: String?, scmId: String?) {
        SpmLogUtil.print("搜索-结果页-列表生成")
        if (params != null) {
            if (context is XyyReportActivity) {
                context.putExtension(SearchReportConstant.EXTENSION_SEARCH_GOODS_SCM_ID, scmId)
            }
            val listCreateBean = SearchProductParams(params, dynamicLabelSelectedMap, selectedShopNames).parseFilterParams()
            listCreateBean.qtListData = qtListData
            SpmUtil.checkAnalysisContextAndEnable(context) {
                ReportUtil.listCreate(context, listCreateBean, trackData?.spmEntity, trackData?.scmEntity)
            }
        }
    }

    /**
     * 单品运营位设置spmD
     */
    private fun setInfoForSpmD(context: Context, rowsBeanInfo: IRowsBeanInfo?, unOpSpmD: String?, btnText: String = ""): SpmBean? {
        return SpmUtil.checkAnalysisContextReturn(context) {
            it.getSpmCtn()?.newInstance()?.apply {
                spmD = if (rowsBeanInfo?.onOpSingleGoods() == true) {
                    //单商品运营位
                    "operation@${rowsBeanInfo.getOPRowsBeanInfo()?.getRank()}_prod@${rowsBeanInfo.getOPRowsBeanInfo()?.getRank()}-${rowsBeanInfo.getOPRowsBeanInfo()?.getSubRank()}$btnText"
                } else if(rowsBeanInfo?.isSingleGroupPurchase() == true) {
                    //加价购
                    "purchase@${rowsBeanInfo.getGroupGoodsPlaceInfo()?.getRank()}_prod@${rowsBeanInfo.getGroupGoodsPlaceInfo()?.getRank()}-${rowsBeanInfo.getGroupGoodsPlaceInfo()?.getSubRank()}$btnText"
                } else if(rowsBeanInfo?.isMultipleGroupPurchase() == true){
                    //组合购
                    "recommend@${rowsBeanInfo.getGroupGoodsPlaceInfo()?.getRank()}_prod@${rowsBeanInfo.getGroupGoodsPlaceInfo()?.getRank()}-${rowsBeanInfo.getGroupGoodsPlaceInfo()?.getSubRank()}$btnText"
                } else "${unOpSpmD}$btnText"
            }
        }
    }

    /**
     * 单品运营位设置scmC
     */
    private fun setPlaceInfoForScmC(rowsBeanInfo: IRowsBeanInfo?, scm: ScmBean?) {
        if (rowsBeanInfo == null) return
        val isOpSingle = rowsBeanInfo.onOpSingleGoods()
        val isGroupPurchase = rowsBeanInfo.isGroupPurchase()
        if (isOpSingle || isGroupPurchase) {
            val goodsPlaceInfo = if (isOpSingle) rowsBeanInfo.getOPRowsBeanInfo() else rowsBeanInfo.getGroupGoodsPlaceInfo()
            var cgId = goodsPlaceInfo?.getCustomerGroupId()
            var oeId = goodsPlaceInfo?.getOperationExhibitionId()
            if (cgId.isNullOrEmpty()) cgId = "all"
            if (oeId.isNullOrEmpty()) oeId = "0"
            cgId = SpmUtil.checkReportSpmFormat(cgId)
            oeId = SpmUtil.checkReportSpmFormat(oeId)
            scm?.scmC = "${cgId}_$oeId"
        }
    }

    /**
     * 处理spm数据
     */
    private fun setPlaceInfoSpm(spm: SpmBean?, scm: ScmBean?, rowsBeanInfo: IRowsBeanInfo?) {
        if (rowsBeanInfo?.isSingleGroupPurchase() == true || rowsBeanInfo?.isMultipleGroupPurchase() == true) {
            if (spm?.spmC == "0") spm.spmC = "purchaseList@2"
            if (scm?.scmA == null) scm?.scmA = "search"
            if (scm?.scmE.isNullOrEmpty()) scm?.scmE = rowsBeanInfo.getScmId()
            if (scm?.scmB.isNullOrEmpty()) scm?.scmB = "0"
        }
    }

    /**
     * 底部弹窗设置spm和scm
     */
    private fun setBottomPopSpmAndScm(spm: SpmBean?, scm: ScmBean?, rowsBeanInfo: IRowsBeanInfo?, btnNum: String? = null, isSubmit: Boolean = false) {
        if (rowsBeanInfo?.isFromBottomPop() == true && rowsBeanInfo.isSingleGroupPurchase()) {
            spm?.apply {
                spmC = "searchList@3"
                spmD = if (isSubmit) {
                    "ftFloatProd@Z_purchase@1_btn@1"
                } else {
                    "ftFloatProd@Z_purchase@1_prod@1-${rowsBeanInfo.getGroupGoodsPlaceInfo()?.getSubRank()?: ""}"
                }
                if (btnNum != null) {
                    spmD = "${spmD}_btn@$btnNum"
                }
            }
            scm?.apply {
                scmB = rowsBeanInfo.getSearchRecPurchaseStrategyCode()?: "0"
                scmC = "all_0"
            }
        }
    }

    /**
     * 商品曝光
     */
    fun trackSearchGoodsExposure(context: Context, rowsBeanInfo: IRowsBeanInfo?, position: Int) {
        if (context !is XyyReportActivity) return
        if (rowsBeanInfo?.onOpGoods() == true
            && !rowsBeanInfo.onOpSingleGoods()
            && !rowsBeanInfo.isGroupPurchase()) return
        if (context.getSpmCtn() == null) return
        val spm = setInfoForSpmD(context, rowsBeanInfo, if(rowsBeanInfo?.isMainFrequently() == true) "prod@${rowsBeanInfo.getOPRowsBeanInfo()?.getRank()}" else "prod@${position+1}")
        val scm = context.getScmCnt()?.newInstance()?.apply {
            scmD = "prod-${rowsBeanInfo?.getSpmProductId()}"
            scmE = context.getExtensionValue(SpmExtensionConstant.EXTENSION_GOODS_SCM_ID)?.toString()
            if (scmB == null) {
                scmB = "0"
            }
            if (rowsBeanInfo?.isFromBottomPop() == true && rowsBeanInfo.isSingleGroupPurchase()) {
                scmE = rowsBeanInfo.getScmId()
            } else if (rowsBeanInfo?.isReplenish() == true) {
                scmE = rowsBeanInfo.getScmId()
            }
        }
        if (spm?.spmB?.contains("shoppingCart") == true) return
        setPlaceInfoForScmC(rowsBeanInfo, scm)
        if (rowsBeanInfo?.isFromBottomPop() == true && rowsBeanInfo.isSingleGroupPurchase()) {
            SpmLogUtil.print("搜索-结果页-底部弹窗-组合购-商品曝光")
        } else if (rowsBeanInfo?.isSingleGroupPurchase() == true) {
            SpmLogUtil.print("搜索-结果页-组合购-商品曝光")
        } else if (rowsBeanInfo?.isMultipleGroupPurchase() == true) {
            SpmLogUtil.print("搜索-结果页-加价购-商品曝光")
        } else {
            SpmLogUtil.print("搜索-结果页-商品曝光")
        }
        setPlaceInfoSpm(spm, scm, rowsBeanInfo)
        setBottomPopSpmAndScm(spm, scm, rowsBeanInfo)
        val goodsExposure = ReportPageSubModuleSearchGoodsExposureBean().apply {
            this.productId = rowsBeanInfo?.getSpmProductId()
            this.productName = rowsBeanInfo?.getSpmProductName()
            if (rowsBeanInfo?.getQtListData() != null) {
                this.qtListData = rowsBeanInfo.getQtListData()
            }
            this.qtSkuData = rowsBeanInfo?.getSpmQtSkuData()
        }
        SpmUtil.checkAnalysisContextAndEnable(context) {
            ReportUtil.goodsExposureTrack(context, goodsExposure, spm, scm)
        }
    }

    /**
     * 商品点击
     */
    @JvmStatic
    fun trackSearchGoodsClick(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?): String? {
        if (context !is XyyReportActivity) return null
        if (rowsBeanInfo?.onOpGoods() == true
            && !rowsBeanInfo.onOpSingleGoods()
            && !rowsBeanInfo.isGroupPurchase()) return null
        if (!isNeedTrack(context)) return null
        val spm = setInfoForSpmD(context, rowsBeanInfo, if(rowsBeanInfo?.isMainFrequently() == true) "prod@${rowsBeanInfo.getOPRowsBeanInfo()?.getRank()}" else "prod@${position+1}")
        val scmId = context.getExtensionValue(SpmExtensionConstant.EXTENSION_GOODS_SCM_ID)?.toString()
        if (spm?.spmB?.contains("shoppingCart") == true) return null
        val scmE = if (rowsBeanInfo?.isReplenish() == true) {
            "${rowsBeanInfo.getScmId()}${SessionManager.get().newGoodsScmRandom()}"
        } else if(rowsBeanInfo?.isFromBottomPop() == true && rowsBeanInfo.isSingleGroupPurchase()) {
            "${rowsBeanInfo.getScmId()}${SessionManager.get().newGoodsScmRandom()}"
        } else if (scmId.isNullOrEmpty()) {
            "${rowsBeanInfo?.getScmId()}${SessionManager.get().newGoodsScmRandom()}"
        } else {
            "${context.getExtensionValue(SpmExtensionConstant.EXTENSION_GOODS_SCM_ID)?.toString()}${SessionManager.get().newGoodsScmRandom()}"
        }
        val scm = context.getScmCnt()?.newInstance()?.apply {
            scmD = "prod-${rowsBeanInfo?.getSpmProductId()}"
        }
        setPlaceInfoSpm(spm, scm, rowsBeanInfo)
        setPlaceInfoForScmC(rowsBeanInfo, scm)
        scm?.scmE = scmE
        if(rowsBeanInfo?.isFromBottomPop() == true && rowsBeanInfo.isSingleGroupPurchase()) {
            SpmLogUtil.print("搜索-结果页-底部弹窗-组合购-商品点击")
        } else if (rowsBeanInfo?.isSingleGroupPurchase() == true) {
            SpmLogUtil.print("搜索-结果页-组合购-商品点击")
        } else if (rowsBeanInfo?.isMultipleGroupPurchase() == true) {
            SpmLogUtil.print("搜索-结果页-加价购-商品点击")
        } else {
            SpmLogUtil.print("搜索-结果页-商品点击")
        }
        setBottomPopSpmAndScm(spm, scm, rowsBeanInfo)
        val goodsClick = ReportActionSubModuleSearchGoodsClickBean().apply {
            this.productId = rowsBeanInfo?.getSpmProductId()
            this.productName = rowsBeanInfo?.getSpmProductName()
            if (rowsBeanInfo?.getQtListData() != null) {
                this.qtListData = rowsBeanInfo.getQtListData()
            }
            this.qtSkuData = rowsBeanInfo?.getSpmQtSkuData()
        }
        SpmUtil.checkAnalysisContextAndEnable(context) {
            ReportUtil.track(context, goodsClick, spm, scm)
        }
        return scmE
    }

    /**
     * 商品按钮点击
     */
    private fun trackSearchItemBtnClick(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?, btnNum: String?, content: String?, trackDataBlock: ((TrackData?)->Unit)? = null, block: (()->Unit)? = null) {
        if (context !is XyyReportActivity) return
        if (rowsBeanInfo?.onOpGoods() == true
            && !rowsBeanInfo.onOpSingleGoods()
            && !rowsBeanInfo.isGroupPurchase()) return
        if (context.getScmCnt() == null) return
        if (!isNeedTrack(context)) return
        val scmE = trackSearchGoodsClick(context, position, rowsBeanInfo)
        block?.invoke()
        val spm = setInfoForSpmD(context, rowsBeanInfo, if(rowsBeanInfo?.isMainFrequently() == true) "prod@${rowsBeanInfo.getOPRowsBeanInfo()?.getRank()}" else "prod@${position+1}", "_btn@$btnNum")
        val scm = context.getScmCnt()?.newInstance()?.apply {
            scmD = "prod-${rowsBeanInfo?.getSpmProductId()}_text-$content"
            this.scmE = scmE
        }
        if (spm?.spmB?.contains("shoppingCart") == true) return
        setPlaceInfoForScmC(rowsBeanInfo, scm)
        val goodsBtnClick = ReportActionSearchProductButtonClickBean().apply {
            this.productId = rowsBeanInfo?.getSpmProductId()
            this.productName = rowsBeanInfo?.getSpmProductName()
            if (rowsBeanInfo?.getQtListData() != null) {
                this.qtListData = rowsBeanInfo.getQtListData()
            }
            this.qtSkuData = rowsBeanInfo?.getSpmQtSkuData()
        }
        setPlaceInfoSpm(spm, scm, rowsBeanInfo)
        setBottomPopSpmAndScm(spm, scm, rowsBeanInfo, btnNum)
        AddCartPopupWindowReport.addExtensionForAddCartPopupWindow(context, spm, scm, true)
        trackDataBlock?.invoke(TrackData(spm, scm))
        SpmUtil.checkAnalysisContextAndEnable(context) {
            ReportUtil.track(context, goodsBtnClick, spm, scm)
        }
    }

    /**
     * 组合购or加价购去下单按钮点击获取qtskudata
     */
    private fun getSearchPlaceOrderQtSkuData(context: XyyReportActivity, mainRowsBeanInfo: IRowsBeanInfo?, subGoodsList: List<IRowsBeanInfo>?, orgQtSkuData: String?): String? {
        if (mainRowsBeanInfo == null || subGoodsList == null || orgQtSkuData == null) return null
        val orgQtSkuDataMap = Gson().fromJson<Map<String, Any?>>(orgQtSkuData, object: TypeToken<Map<String, Any?>>() {}.type)
        val subProductIdGroups = mutableListOf<String>()
        val subProductNameGroups = mutableListOf<String>()
        val subProductSubRankGroups = mutableListOf<String>()
        subGoodsList.forEach {
            subProductIdGroups.add("${it.getSpmProductId()}")
            subProductNameGroups.add(SpmUtil.checkReportSpmFormat(it.getSpmProductName())?: "")
            subProductSubRankGroups.add(it.getGroupGoodsPlaceInfo()?.getSubRank()?.toString()?: "")
        }
        val scmId = mainRowsBeanInfo.getScmId()?: context.getExtensionValue(SpmExtensionConstant.EXTENSION_GOODS_SCM_ID)
        val qtSkuDataMap = mapOf(
            "list_position_type" to (orgQtSkuDataMap["list_position_type"]?: ""),
            "list_position_type_name" to (orgQtSkuDataMap["list_position_type_name"]?: ""),
            "main_product_id" to (mainRowsBeanInfo.getSpmProductId()),
            "main_product_name" to (SpmUtil.checkReportSpmFormat(mainRowsBeanInfo.getSpmProductName())?: ""),
            "scm_id" to if(mainRowsBeanInfo.isFromBottomPop() && mainRowsBeanInfo.isSingleGroupPurchase()) mainRowsBeanInfo.getScmId() else scmId,
            "sub_product_id_groups" to subProductIdGroups,
            "sub_product_name_groups" to subProductNameGroups,
            "sub_product_sub_rank_groups" to subProductSubRankGroups,
            "sub_product_num" to subGoodsList.size,
        )
        return Gson().toJson(qtSkuDataMap)
    }

    /**
     * 组合购or加价购去下单按钮点击
     */
    @JvmStatic
    fun trackSearchPlaceOrderClick(context: Context, mainRowsBeanInfo: IRowsBeanInfo?, subGoodsList: List<IRowsBeanInfo>, isSearch: Boolean = false, isSingleCombine: Boolean = false) {
        if (context !is XyyReportActivity) return
        if (context.getScmCnt() == null) return
        if (!isNeedTrack(context)) return
        if (mainRowsBeanInfo?.isSingleGroupPurchase() == true && mainRowsBeanInfo.getCombinationSelectedStatus() == 0) return
        //已加购的副品
        val selectedSubGoodsList = subGoodsList.filter { it.getCombinationSelectedStatus() == 1 }
        if (selectedSubGoodsList.isEmpty()) return
        val spm = context.getSpmCtn()?.newInstance()?.apply {
            spmD = "purchase@${mainRowsBeanInfo?.getGroupGoodsPlaceInfo()?.getRank()}_btn@1"
        }
        val scm = context.getScmCnt()?.newInstance()?.apply {
            scmD = "prod-${mainRowsBeanInfo?.getSpmProductId()}_text-${if(isSingleCombine) "一起买更优惠" else "去下单"}"
        }
        setPlaceInfoForScmC(mainRowsBeanInfo, scm)
        if (scm?.scmA.isNullOrEmpty()) scm?.scmA = "search"
        if (scm?.scmB.isNullOrEmpty()) scm?.scmB = "0"
        val goodsBtnClick = ReportActionSubModuleGroupCombinationClickBean().apply {
            try {
                if (mainRowsBeanInfo?.isFromBottomPop() == true && mainRowsBeanInfo.isSingleGroupPurchase()) {
                    qtListData = mainRowsBeanInfo.getQtListData()
                } else if (isSearch) {
                    qtListData = if (mainRowsBeanInfo?.isMultipleGroupPurchase() == true && mainRowsBeanInfo.getQtListData() == null) {
                        subGoodsList[0].getQtListData()
                    } else {
                        mainRowsBeanInfo?.getQtListData()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            this.qtSkuData = getSearchPlaceOrderQtSkuData(context, mainRowsBeanInfo, selectedSubGoodsList, mainRowsBeanInfo?.getSpmQtSkuData())
        }
        setPlaceInfoSpm(spm, scm, mainRowsBeanInfo)
        SpmUtil.setScmE(scm)
        setBottomPopSpmAndScm(spm, scm, mainRowsBeanInfo, isSubmit = true)
        if (mainRowsBeanInfo?.isFromBottomPop() == true && mainRowsBeanInfo.isSingleGroupPurchase()) {
            SpmLogUtil.print("搜索-底部弹窗-组合购or加价购-去下单按钮点击")
        } else {
            SpmLogUtil.print("搜索-组合购or加价购-去下单按钮点击")
        }
        SpmUtil.checkAnalysisContextAndEnable(context) {
            ReportUtil.track(context, goodsBtnClick, spm, scm)
        }
    }

    fun trackSearchItemBtnClickSpellGroup(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_SPELL_GROUP, "参团"){
            SpmLogUtil.print("搜索-按钮点击-立即参团")
        }
    }

    fun trackSearchItemBtnClickWholeSale(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_WHOLE_SALE, "抢购") {
            SpmLogUtil.print("搜索-按钮点击-去抢购")
        }
    }

    fun trackSearchItemBtnClickAddCart(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_ADD_CART, "加购", {trackData ->
            AddCartPopupWindowReport.addCartActionTrack(context, trackData)
        }) {
            SpmLogUtil.print("搜索-按钮点击-加购")
        }
    }

    fun trackSearchItemBtnClickSubtract(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_SUBTRACT, "减", {trackData ->
            AddCartPopupWindowReport.addCartActionTrack(context, trackData)
        }) {
            SpmLogUtil.print("搜索-按钮点击-减")
        }
    }

    fun trackSearchItemBtnClickAddCount(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?, content: String?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_ADD_COUNT, content, {trackData->
            AddCartPopupWindowReport.addCartActionTrack(context, trackData)
        }) {
            SpmLogUtil.print("搜索-按钮点击-加购数量")
        }
    }

    fun trackSearchItemBtnClickAdd(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_ADD, "加", {trackData ->
            AddCartPopupWindowReport.addCartActionTrack(context, trackData)
        }){
            SpmLogUtil.print("搜索-按钮点击-加")
        }
    }

    fun trackSearchItemBtnClickMore(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_MORE, "更多优惠") {
            SpmLogUtil.print("搜索-按钮点击-更多优惠")
        }
    }

    fun trackSearchItemBtnClickShopEntry(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?, content: String?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_SHOP_ENTRY, content) {
            SpmLogUtil.print("搜索-按钮点击-进店")
        }
    }

    fun trackSearchItemBtnClickSingleOpShopEntry(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?, content: String?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_SHOP_ENTRY, content) {
            SpmLogUtil.print("搜索-按钮点击-进店(活动)")
        }
    }

    fun trackSearchItemBtnClickShopSame(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?, content: String?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_SHOP_SAME, content) {
            SpmLogUtil.print("搜索-按钮点击-店铺同款")
        }
    }


    fun trackGroupCombinationItemClickSubtract(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_SUBTRACT, "减", {trackData ->
            AddCartPopupWindowReport.addCartActionTrack(context, trackData)
        }) {
            SpmLogUtil.print("搜索-组合购or加价购-按钮点击-减")
        }
    }

    fun trackGroupCombinationBtnClickAddCount(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?, content: String?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_ADD_COUNT, content, {trackData->
            AddCartPopupWindowReport.addCartActionTrack(context, trackData)
        }) {
            SpmLogUtil.print("搜索-组合购or加价购-按钮点击-加购数量")
        }
    }

    fun trackGroupCombinationBtnClickAdd(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_ADD, "加", {trackData ->
            AddCartPopupWindowReport.addCartActionTrack(context, trackData)
        }){
            SpmLogUtil.print("搜索-组合购or加价购-按钮点击-加")
        }
    }

    fun trackGroupCombinationBtnClickWholeSale(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_WHOLE_SALE, "抢购") {
            SpmLogUtil.print("搜索-加价购-抢购")
        }
    }

    fun trackGroupCombinationBtnClickAddCart(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_ADD_CART, "加购", {trackData ->
            AddCartPopupWindowReport.addCartActionTrack(context, trackData)
        }) {
            SpmLogUtil.print("搜索-加价购-加购")
        }
    }

    fun trackGroupCombinationBtnClickSpellGroup(context: Context, position: Int, rowsBeanInfo: IRowsBeanInfo?) {
        trackSearchItemBtnClick(context, position, rowsBeanInfo, SEARCH_ITEM_BTN_CLICK_SPELL_GROUP, "参团"){
            SpmLogUtil.print("搜索-加价购-参团")
        }
    }













    private fun getQtListData(context: Context): String? {
        return if (context is XyyReportActivity) {
            context.getExtensionValue("qtListData")?.toString()
        } else null
    }

    private fun trackSearch(context: Context, iReport: IReport?, spm: ISpm?, scm: ISpm?) {
        if (iReport != null) {
            if (context is XyyReportActivity && context.getEnableAnalysis()) {
                ReportUtil.track(context, iReport, spm, scm)
            }
        }
    }

    private fun isNeedTrack(context: Context): Boolean {
        return if (context is XyyReportActivity) {
            context.getExtensionValue(SpmExtensionConstant.EXTENSION_GOODS_SCM_ID) != null
        } else false
    }

}
package com.ybmmarket20.xyyreport.page.goodDetail

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportPageComponentExposureBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.ISpm
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

object AdReportEvent {

    //子模块点击
    @JvmStatic
    fun trackAdSubModuleClick(context: Context, spmStr: String?, scmStr: String?, spmCtn: SpmBean?) {
        val clickBean = ReportActionSubModuleClickBean()
        SpmLogUtil.print("商品详情页子模块点击")
        val spm = SpmUtil.getSpmBeanFromStr(spmStr)
        spm.apply {
            spmB = spmCtn?.spmB
            spmE = spmCtn?.spmE
        }
        SpmUtil.fixSpmESession(spm)
        val scm = SpmUtil.getScmBeanFromStr(scmStr)
        SpmUtil.setScmE(scm)
        ReportUtil.track(context, clickBean, spm, scm)
    }
}
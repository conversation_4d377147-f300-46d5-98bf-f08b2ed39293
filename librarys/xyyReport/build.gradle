plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
}

android {
    compileSdkVersion 30

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 30
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "qt_app_key", '"3p07vcxxj6fo1wmocilsopv2"'
        }

        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "qt_app_key", '"gp4ewsfsnmg347oiji11rbyg"'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
        freeCompilerArgs += ['-Xskip-metadata-version-check']
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.8.0'
    implementation platform('org.jetbrains.kotlin:kotlin-bom:1.7.10')
    implementation 'com.google.code.gson:gson:2.8.6'
    //qt埋点
    api('com.lydaas.qtsdk:qt-px-common:1.8.0.PX') {
        exclude group: 'com.android.support'
    }
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    kapt 'com.xyy.ybm100:xyyReportCompiler:1.0.6'
    implementation 'com.xyy.ybm100:xyyReport:1.0.6'
    implementation project(path: ':common')
}
plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode 1
        versionName "1.0"
        consumerProguardFiles 'consumer-proguard-rules.pro'
    }
    lintOptions {
        abortOnError false
        ignoreWarnings true
        baseline file("lint-baseline.xml")
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'androidx.recyclerview:recyclerview:1.1.0'

    //EventBus
    api 'org.greenrobot:eventbus:3.2.0'
    implementation 'com.tencent.tdos-diagnose:diagnose:0.4.11'
    implementation 'com.tencent.tdos-diagnose:logger:0.4.11'
}

<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

	<!-- disable -->
	<item android:state_checked="true" android:state_enabled="false">
		<layer-list>
			<item>
				<shape android:shape="oval">
					<size
						android:width="@dimen/ksw_md_thumb_ripple_size"
						android:height="@dimen/ksw_md_thumb_ripple_size"/>
					<solid android:color="@android:color/transparent"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_shadow_inset_bottom"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_shadow_inset"
				android:right="@dimen/ksw_md_thumb_shadow_inset"
				android:top="@dimen/ksw_md_thumb_shadow_inset_top">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_shadow"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_solid_inset"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_solid_inset"
				android:right="@dimen/ksw_md_thumb_solid_inset"
				android:top="@dimen/ksw_md_thumb_solid_inset">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_checked_disable"/>
				</shape>
			</item>
		</layer-list>
	</item>

	<!-- disable -->
	<item android:state_enabled="false">
		<layer-list>
			<item>
				<shape android:shape="oval">
					<size
						android:width="@dimen/ksw_md_thumb_ripple_size"
						android:height="@dimen/ksw_md_thumb_ripple_size"/>
					<solid android:color="@android:color/transparent"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_shadow_inset_bottom"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_shadow_inset"
				android:right="@dimen/ksw_md_thumb_shadow_inset"
				android:top="@dimen/ksw_md_thumb_shadow_inset_top">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_shadow"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_solid_inset"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_solid_inset"
				android:right="@dimen/ksw_md_thumb_solid_inset"
				android:top="@dimen/ksw_md_thumb_solid_inset">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_disable"/>
				</shape>
			</item>
		</layer-list>
	</item>

	<!-- checked pressed -->
	<item android:state_checked="true" android:state_pressed="true">
		<layer-list>
			<item>
				<shape android:shape="oval">
					<size
						android:width="@dimen/ksw_md_thumb_ripple_size"
						android:height="@dimen/ksw_md_thumb_ripple_size"/>
					<solid android:color="@color/ksw_md_ripple_checked"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_shadow_inset_bottom"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_shadow_inset"
				android:right="@dimen/ksw_md_thumb_shadow_inset"
				android:top="@dimen/ksw_md_thumb_shadow_inset_top">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_shadow"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_solid_inset"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_solid_inset"
				android:right="@dimen/ksw_md_thumb_solid_inset"
				android:top="@dimen/ksw_md_thumb_solid_inset">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_checked"/>
				</shape>
			</item>
		</layer-list>
	</item>

	<!-- unchecked pressed -->
	<item android:state_checked="false" android:state_pressed="true">
		<layer-list>
			<item>
				<shape android:shape="oval">
					<size
						android:width="@dimen/ksw_md_thumb_ripple_size"
						android:height="@dimen/ksw_md_thumb_ripple_size"/>
					<solid android:color="@color/ksw_md_ripple_normal"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_shadow_inset_bottom"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_shadow_inset"
				android:right="@dimen/ksw_md_thumb_shadow_inset"
				android:top="@dimen/ksw_md_thumb_shadow_inset_top">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_shadow"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_solid_inset"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_solid_inset"
				android:right="@dimen/ksw_md_thumb_solid_inset"
				android:top="@dimen/ksw_md_thumb_solid_inset">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_normal"/>
				</shape>
			</item>
		</layer-list>
	</item>

	<!-- checked unpressed -->
	<item android:state_checked="true" android:state_pressed="false">
		<layer-list>
			<item>
				<shape android:shape="oval">
					<size
						android:width="@dimen/ksw_md_thumb_ripple_size"
						android:height="@dimen/ksw_md_thumb_ripple_size"/>
					<solid android:color="@android:color/transparent"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_shadow_inset_bottom"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_shadow_inset"
				android:right="@dimen/ksw_md_thumb_shadow_inset"
				android:top="@dimen/ksw_md_thumb_shadow_inset_top">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_shadow"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_solid_inset"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_solid_inset"
				android:right="@dimen/ksw_md_thumb_solid_inset"
				android:top="@dimen/ksw_md_thumb_solid_inset">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_checked"/>
				</shape>
			</item>
		</layer-list>
	</item>

	<!-- normal -->
	<item>
		<layer-list>
			<item>
				<shape android:shape="oval">
					<size
						android:width="@dimen/ksw_md_thumb_ripple_size"
						android:height="@dimen/ksw_md_thumb_ripple_size"/>
					<solid android:color="@android:color/transparent"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_shadow_inset_bottom"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_shadow_inset"
				android:right="@dimen/ksw_md_thumb_shadow_inset"
				android:top="@dimen/ksw_md_thumb_shadow_inset_top">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_shadow"/>
				</shape>
			</item>
			<item
				android:bottom="@dimen/ksw_md_thumb_solid_inset"
				android:gravity="center"
				android:left="@dimen/ksw_md_thumb_solid_inset"
				android:right="@dimen/ksw_md_thumb_solid_inset"
				android:top="@dimen/ksw_md_thumb_solid_inset">
				<shape android:shape="oval">
					<solid android:color="@color/ksw_md_solid_normal"/>
				</shape>
			</item>
		</layer-list>
	</item>
</selector>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:id="@id/status_view_btn_reload"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F7F7F8"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/status_view_iv_no_network"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:src="@drawable/icon_no_network"/>

    <TextView
        android:id="@+id/status_view_tv_no_network"
        style="@style/status_view_message_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/status_view_net_exception_text"/>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/bt_afreshLoad"
        style="@style/status_view_message_text"
        android:layout_width="wrap_content"
        android:paddingLeft="40dp"
        android:paddingRight="40dp"
        android:layout_height="40dp"
        android:layout_marginLeft="33dp"
        android:layout_marginRight="33dp"
        android:layout_marginTop="40dp"
        app:rv_backgroundColor="#FFF"
        app:rv_cornerRadius="50dp"
        app:rv_strokeWidth="1dp"
        app:rv_strokeColor="#00B377"
        android:textColor="#00B377"
        android:gravity="center"
        android:text="@string/status_view_error_text_click_reload"/>

</LinearLayout>

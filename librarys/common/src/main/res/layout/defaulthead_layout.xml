<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="horizontal">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/default_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?android:attr/actionBarSize"
        app:navigationIcon="@drawable/ic_back">

        <TextView
            android:id="@+id/tv_toolbar_title"
            android:layout_width="210dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="1"
            android:textStyle="bold"
            android:text=""
            android:textColor="@color/text_292933"
            android:textSize="16sp"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="right"
            android:layout_marginRight="5dp"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_rightText"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:text=""
                android:textColor="@color/text_292933"
                android:textSize="14sp"
                android:visibility="visible"
                tools:text="成熟" />

            <ImageView
                android:id="@+id/iv_rightBt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>

    </androidx.appcompat.widget.Toolbar>

</RelativeLayout>
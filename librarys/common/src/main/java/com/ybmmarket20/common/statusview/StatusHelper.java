package com.ybmmarket20.common.statusview;

import android.app.Activity;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

/**
 * @author: yuhaibo
 * @time: 2017/12/21 11:34.
 * projectName:
 * Description:动态添加到根布局
 */
public class StatusHelper {
    public static StatusViewLayout setContentView(Activity activity, int layoutResID) {
        StatusViewLayout layout = new StatusViewLayout(activity);
        layout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        View contentView = LayoutInflater.from(activity).inflate(layoutResID, layout, false);
        layout.addView(contentView);
        layout.showContent();
        activity.setContentView(layout);
        return layout;
    }

    public static StatusViewLayout setContentView(Activity activity, View contentView) {
        StatusViewLayout layout = new StatusViewLayout(activity);
        layout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        layout.addView(contentView);
        layout.showContent();
        activity.setContentView(layout);
        return layout;
    }

    public static StatusViewLayout onCreateView(Fragment fragment, int layoutResID) {
        StatusViewLayout layout = new StatusViewLayout(fragment.getContext());
        layout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        View rootView = LayoutInflater.from(fragment.getContext()).inflate(layoutResID, layout, false);
        layout.addView(rootView);
        layout.showContent();
        return layout;
    }

    public static StatusViewLayout onCreateView(Fragment fragment, View rootView) {
        StatusViewLayout layout = new StatusViewLayout(fragment.getContext());
        layout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        layout.addView(rootView);
        layout.showContent();
        return layout;
    }

}
